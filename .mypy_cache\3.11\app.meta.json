{"data_mtime": 1749714646, "dep_lines": [13, 14, 15, 16, 17, 1, 3, 4, 7, 8, 9, 10, 19, 20, 21, 22, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 18], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["my_code.web_code.user", "my_code.web_code.radar_manage", "my_code.web_code.data_analysis", "my_code.web_code.radar_information", "my_code.web_code.scene_parameter", "flask", "typing", "logging", "os", "json", "flask_jwt_extended", "config", "validator", "type", "database", "bson", "enum", "builtins", "_frozen_importlib", "_typeshed", "abc", "bson.objectid", "datetime", "flask.app", "flask.blueprints", "flask.config", "flask.helpers", "flask.json", "flask.sansio", "flask.sansio.app", "flask.sansio.blueprints", "flask.sansio.scaffold", "flask.wrappers", "flask_jwt_extended.jwt_manager", "flask_jwt_extended.utils", "flask_jwt_extended.view_decorators", "json.decoder", "pymongo", "pymongo.common", "pymongo.synchronous", "pymongo.synchronous.mongo_client", "types", "typing_extensions", "validator.validator_framework", "werkzeug", "werkzeug.datastructures", "werkzeug.datastructures.headers", "werkzeug.sansio", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.response", "wsgiref", "wsgiref.types"], "hash": "b26ec3f09d25152c6b4835c50b01fb3b2346a03d", "id": "app", "ignore_all": false, "interface_hash": "0a0601b52d3459e1cd8d117216a8179421a2580e", "mtime": 1749714646, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\app.py", "plugin_data": null, "size": 7171, "suppressed": ["flask_cors", "utils"], "version_id": "1.15.0"}